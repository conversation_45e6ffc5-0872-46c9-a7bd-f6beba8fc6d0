<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Quiz;
use App\Models\QuizQuestion;
use App\Models\QuizCategory;
use App\Models\LandingPage;
use Illuminate\Http\Request;

class QuizAdminController extends Controller
{
    /**
     * Display the quiz for a landing page.
     */
    public function show(LandingPage $landingPage)
    {
        $quiz = $landingPage->quiz()->with(['questions.category', 'categories'])->first();

        if (!$quiz) {
            // Create a default quiz if none exists
            $quiz = Quiz::create([
                'landing_page_id' => $landingPage->id,
                'title' => 'Assessment Quiz',
                'description' => 'Please answer the following questions to help us understand your needs.',
                'is_active' => true,
            ]);
        }

        $categories = $quiz->categories()->ordered()->get();

        return view('admin.quizzes.show', compact('landingPage', 'quiz', 'categories'));
    }

    /**
     * Show the score configuration page for a quiz.
     */
    public function scoreConfig(LandingPage $landingPage)
    {
        $quiz = $landingPage->quiz;

        if (!$quiz) {
            return redirect()->route('admin.landing-pages.quiz', $landingPage)
                ->with('error', 'Please create a quiz first before configuring score settings.');
        }

        $gaugeConfig = $quiz->getGaugeConfig();
        $resultTexts = $quiz->getResultTexts();

        return view('admin.quizzes.score-config', compact('landingPage', 'quiz', 'gaugeConfig', 'resultTexts'));
    }

    /**
     * Update the score configuration for a quiz.
     */
    public function updateScoreConfig(Request $request, LandingPage $landingPage)
    {
        $quiz = $landingPage->quiz;

        if (!$quiz) {
            return redirect()->route('admin.landing-pages.quiz', $landingPage)
                ->with('error', 'Quiz not found.');
        }

        $validated = $request->validate([
            'gauge_config' => 'nullable|array',
            'gauge_config.min_value' => 'required|integer|min:0',
            'gauge_config.max_value' => 'required|integer|min:1',
            'gauge_config.show_percentage' => 'boolean',
            'gauge_config.gauge_type' => 'required|in:semicircle,full_circle,linear',
            'gauge_config.show_labels' => 'boolean',
            'gauge_config.animate' => 'boolean',
            'gauge_config.color_ranges' => 'required|array|min:1',
            'gauge_config.color_ranges.*.min' => 'required|integer|min:0',
            'gauge_config.color_ranges.*.max' => 'required|integer',
            'gauge_config.color_ranges.*.color' => 'required|string',
            'gauge_config.color_ranges.*.label' => 'required|string|max:255',
            'result_texts' => 'nullable|array',
            'result_texts.ranges' => 'required|array|min:1',
            'result_texts.ranges.*.min' => 'required|integer|min:0',
            'result_texts.ranges.*.max' => 'required|integer',
            'result_texts.ranges.*.title' => 'required|string|max:255',
            'result_texts.ranges.*.description' => 'required|string',
            'result_texts.ranges.*.recommendations' => 'nullable|array',
            'result_texts.ranges.*.recommendations.*' => 'string|max:255',
        ]);

        // Ensure boolean values are properly set
        $validated['gauge_config']['show_percentage'] = $request->has('gauge_config.show_percentage');
        $validated['gauge_config']['show_labels'] = $request->has('gauge_config.show_labels');
        $validated['gauge_config']['animate'] = $request->has('gauge_config.animate');

        // Update the quiz with new configuration
        $quiz->update([
            'gauge_config' => $validated['gauge_config'],
            'result_texts' => $validated['result_texts'],
        ]);

        return redirect()->route('admin.landing-pages.quiz.score-config', $landingPage)
            ->with('success', 'Score configuration updated successfully.');
    }

    /**
     * Update the quiz.
     */
    public function update(Request $request, LandingPage $landingPage)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $quiz = $landingPage->quiz()->firstOrFail();
        $quiz->update($validated);

        return redirect()->route('admin.quizzes.show', $landingPage)
            ->with('success', 'Quiz updated successfully.');
    }

    /**
     * Store a new question.
     */
    public function storeQuestion(Request $request, LandingPage $landingPage)
    {
        $validated = $request->validate([
            'question' => 'required|string|max:500',
            'type' => 'required|in:multiple_choice,single_choice,text,scale',
            'category_id' => 'nullable|exists:quiz_categories,id',
            'options' => 'nullable|array',
            'options.*.text' => 'required_if:type,multiple_choice,single_choice|string|max:255',
            'options.*.points' => 'required_if:type,multiple_choice,single_choice|integer|min:0|max:100',
            'points' => 'required|integer|min:1|max:100',
            'is_required' => 'boolean',
        ]);

        // Additional validation for question types
        $this->validateQuestionTypeSpecificRules($validated);

        $quiz = $landingPage->quiz()->firstOrFail();

        // Get the next sort order
        $maxSortOrder = $quiz->questions()->max('sort_order') ?? 0;

        $question = QuizQuestion::create([
            'quiz_id' => $quiz->id,
            'category_id' => $validated['category_id'] ?? null,
            'question' => $validated['question'],
            'type' => $validated['type'],
            'options' => $validated['options'] ?? null,
            'points' => $validated['points'],
            'sort_order' => $maxSortOrder + 1,
            'is_required' => $validated['is_required'] ?? true,
        ]);

        return redirect()->route('admin.quizzes.show', $landingPage)
            ->with('success', 'Question added successfully.');
    }

    /**
     * Update a question.
     */
    public function updateQuestion(Request $request, LandingPage $landingPage, QuizQuestion $question)
    {
        $validated = $request->validate([
            'question' => 'required|string|max:500',
            'type' => 'required|in:multiple_choice,single_choice,text,scale',
            'category_id' => 'nullable|exists:quiz_categories,id',
            'options' => 'nullable|array',
            'options.*.text' => 'required_if:type,multiple_choice,single_choice|string|max:255',
            'options.*.points' => 'required_if:type,multiple_choice,single_choice|integer|min:0|max:100',
            'points' => 'required|integer|min:1|max:100',
            'is_required' => 'boolean',
        ]);

        // Additional validation for question types
        $this->validateQuestionTypeSpecificRules($validated);

        $question->update([
            'question' => $validated['question'],
            'type' => $validated['type'],
            'category_id' => $validated['category_id'] ?? null,
            'options' => $validated['options'] ?? null,
            'points' => $validated['points'],
            'is_required' => $validated['is_required'] ?? true,
        ]);

        return redirect()->route('admin.quizzes.show', $landingPage)
            ->with('success', 'Question updated successfully.');
    }

    /**
     * Delete a question.
     */
    public function destroyQuestion(LandingPage $landingPage, QuizQuestion $question)
    {
        $question->delete();

        return redirect()->route('admin.quizzes.show', $landingPage)
            ->with('success', 'Question deleted successfully.');
    }

    /**
     * Reorder questions.
     */
    public function reorderQuestions(Request $request, LandingPage $landingPage)
    {
        $validated = $request->validate([
            'questions' => 'required|array',
            'questions.*' => 'required|integer|exists:quiz_questions,id',
        ]);

        $quiz = $landingPage->quiz()->firstOrFail();

        foreach ($validated['questions'] as $index => $questionId) {
            QuizQuestion::where('id', $questionId)
                ->where('quiz_id', $quiz->id)
                ->update(['sort_order' => $index + 1]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Validate question type specific rules.
     */
    private function validateQuestionTypeSpecificRules(array $validated): void
    {
        $type = $validated['type'];
        $options = $validated['options'] ?? [];

        switch ($type) {
            case 'multiple_choice':
            case 'single_choice':
                // Ensure options are provided for choice questions
                if (empty($options)) {
                    throw new \Illuminate\Validation\ValidationException(
                        validator([], []),
                        ['options' => ['Options are required for choice questions.']]
                    );
                }

                // Ensure at least 2 options
                if (count($options) < 2) {
                    throw new \Illuminate\Validation\ValidationException(
                        validator([], []),
                        ['options' => ['At least 2 options are required for choice questions.']]
                    );
                }

                // Ensure no duplicate option texts
                $optionTexts = array_column($options, 'text');
                if (count($optionTexts) !== count(array_unique($optionTexts))) {
                    throw new \Illuminate\Validation\ValidationException(
                        validator([], []),
                        ['options' => ['Option texts must be unique.']]
                    );
                }
                break;

            case 'scale':
                // Scale questions shouldn't have options
                if (!empty($options)) {
                    throw new \Illuminate\Validation\ValidationException(
                        validator([], []),
                        ['options' => ['Scale questions should not have options.']]
                    );
                }

                // Scale questions should have points between 1-10
                if ($validated['points'] > 10) {
                    throw new \Illuminate\Validation\ValidationException(
                        validator([], []),
                        ['points' => ['Scale questions should have maximum 10 points.']]
                    );
                }
                break;

            case 'text':
                // Text questions shouldn't have options
                if (!empty($options)) {
                    throw new \Illuminate\Validation\ValidationException(
                        validator([], []),
                        ['options' => ['Text questions should not have options.']]
                    );
                }
                break;
        }
    }
}
