<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Quiz extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'landing_page_id',
        'title',
        'description',
        'scoring_rules',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'scoring_rules' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the landing page that owns this quiz.
     */
    public function landingPage(): BelongsTo
    {
        return $this->belongsTo(LandingPage::class);
    }

    /**
     * Get the questions for this quiz.
     */
    public function questions(): HasMany
    {
        return $this->hasMany(QuizQuestion::class)->orderBy('sort_order');
    }

    /**
     * Get the categories for this quiz.
     */
    public function categories(): HasMany
    {
        return $this->hasMany(QuizCategory::class)->orderBy('sort_order');
    }

    /**
     * Get the active categories for this quiz.
     */
    public function activeCategories(): HasMany
    {
        return $this->categories()->where('is_active', true);
    }

    /**
     * Calculate the total possible score for this quiz.
     */
    public function getTotalPossibleScore(): int
    {
        return $this->questions()->sum('points');
    }

    /**
     * Calculate score from responses.
     */
    public function calculateScore(array $responses): int
    {
        $score = 0;
        $questions = $this->questions()->get();

        foreach ($questions as $question) {
            $response = $responses[$question->id] ?? null;

            if ($response !== null) {
                $score += $this->calculateQuestionScore($question, $response);
            }
        }

        return $score;
    }

    /**
     * Calculate score for a single question.
     */
    private function calculateQuestionScore(QuizQuestion $question, $response): int
    {
        switch ($question->type) {
            case 'multiple_choice':
                // For multiple choice, response is an array of selected options
                if (is_array($response)) {
                    $score = 0;
                    $options = $question->options;
                    foreach ($response as $selectedOption) {
                        if (isset($options[$selectedOption]['points'])) {
                            $score += $options[$selectedOption]['points'];
                        }
                    }
                    return $score;
                }
                break;

            case 'single_choice':
                // For single choice, response is a single value
                $options = $question->options;
                if (isset($options[$response]['points'])) {
                    return $options[$response]['points'];
                }
                break;

            case 'scale':
                // For scale questions, response is the scale value
                return (int) $response;

            case 'text':
                // Text questions get full points if answered
                return $response ? $question->points : 0;
        }

        return 0;
    }

    /**
     * Validate quiz responses structure and content.
     */
    public function validateResponses(array $responses): array
    {
        $errors = [];
        $questions = $this->questions()->get();

        foreach ($questions as $question) {
            $response = $responses[$question->id] ?? null;

            // Check required questions
            if ($question->is_required && ($response === null || $response === '' || (is_array($response) && empty($response)))) {
                $errors[] = "Question '{$question->question}' is required.";
                continue;
            }

            // Skip validation if not required and empty
            if (!$question->is_required && ($response === null || $response === '')) {
                continue;
            }

            // Validate response format
            $validationResult = $this->validateQuestionResponse($question, $response);
            if (!$validationResult['valid']) {
                $errors = array_merge($errors, $validationResult['errors']);
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate a single question response.
     */
    private function validateQuestionResponse(QuizQuestion $question, $response): array
    {
        $errors = [];

        switch ($question->type) {
            case 'multiple_choice':
                if (!is_array($response)) {
                    $errors[] = "Question '{$question->question}' expects multiple selections.";
                    break;
                }

                $options = $question->getFormattedOptions();
                $validOptions = array_keys($options);

                foreach ($response as $selectedOption) {
                    if (!in_array($selectedOption, $validOptions)) {
                        $errors[] = "Invalid option selected for question '{$question->question}'.";
                    }
                }
                break;

            case 'single_choice':
                if (is_array($response)) {
                    $errors[] = "Question '{$question->question}' expects only one selection.";
                    break;
                }

                $options = $question->getFormattedOptions();
                $validOptions = array_keys($options);

                if (!in_array($response, $validOptions)) {
                    $errors[] = "Invalid option selected for question '{$question->question}'.";
                }
                break;

            case 'text':
                if (!is_string($response)) {
                    $errors[] = "Question '{$question->question}' expects a text response.";
                    break;
                }

                if (strlen(trim($response)) < 2) {
                    $errors[] = "Response for question '{$question->question}' is too short.";
                } elseif (strlen($response) > 1000) {
                    $errors[] = "Response for question '{$question->question}' is too long (maximum 1000 characters).";
                }
                break;

            case 'scale':
                if (!is_numeric($response)) {
                    $errors[] = "Question '{$question->question}' expects a numeric value.";
                    break;
                }

                $value = (int) $response;
                if ($value < 1 || $value > 10) {
                    $errors[] = "Response for question '{$question->question}' must be between 1 and 10.";
                }
                break;
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Check if the quiz is ready for use.
     */
    public function isReady(): bool
    {
        return $this->is_active && $this->questions()->count() > 0;
    }

    /**
     * Get validation errors for the quiz structure.
     */
    public function getStructureValidationErrors(): array
    {
        $errors = [];

        if (!$this->is_active) {
            $errors[] = 'Quiz is not active.';
        }

        $questionCount = $this->questions()->count();
        if ($questionCount === 0) {
            $errors[] = 'Quiz must have at least one question.';
        }

        $requiredQuestions = $this->questions()->where('is_required', true)->count();
        if ($requiredQuestions === 0) {
            $errors[] = 'Quiz should have at least one required question.';
        }

        return $errors;
    }
}
