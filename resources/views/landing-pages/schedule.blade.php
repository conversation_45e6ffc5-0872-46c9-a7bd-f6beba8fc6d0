@extends('layouts.app')

@section('title', 'Schedule Your Consultation - ' . $landingPage->title)

@section('content')
<div class="schedule-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="schedule-header text-center mb-5">
                    <div class="success-icon mb-4">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h1>Assessment Complete!</h1>
                    <p class="lead">Thank you, {{ $lead->name }}! Your personalized report has been generated.</p>
                    @if($lead->quiz_score !== null)
                        <div class="score-display">
                            <div class="score-circle">
                                <span class="score-number">{{ $lead->quiz_score }}</span>
                                <span class="score-label">Score</span>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title text-center mb-4">
                            <i class="fas fa-calendar-alt me-2"></i>
                            Schedule Your Free Consultation
                        </h3>

                        <p class="text-center text-muted mb-4">
                            Based on your assessment results, we'd like to discuss how we can help optimize your business processes.
                            Please let us know your availability for a 30-minute consultation call.
                        </p>

                        <form method="POST" action="{{ route('landing-pages.schedule.submit', $landingPage) }}">
                            @csrf

                            <div class="mb-4">
                                <label for="availability" class="form-label">
                                    <strong>Your Availability</strong>
                                    <small class="text-muted d-block">Please provide your preferred days and times for a consultation call</small>
                                </label>
                                <textarea class="form-control"
                                          id="availability"
                                          name="availability"
                                          rows="6"
                                          required
                                          placeholder="Example:
• Monday-Friday: 9 AM - 5 PM EST
• Best times: Tuesday/Thursday mornings
• Prefer calls between 10 AM - 2 PM
• Available next week except Wednesday
• Time zone: Eastern Standard Time

Please include your time zone and any specific preferences.">{{ old('availability') }}</textarea>
                                @error('availability')
                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="consultation-benefits mb-4">
                                <h5>What to Expect in Your Consultation:</h5>
                                <ul class="benefits-list">
                                    <li><i class="fas fa-check text-success me-2"></i>Personalized analysis of your assessment results</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Custom recommendations for your business</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Discussion of automation opportunities</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Next steps and implementation roadmap</li>
                                    <li><i class="fas fa-check text-success me-2"></i>No obligation - completely free consultation</li>
                                </ul>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-calendar-check me-2"></i>
                                    Schedule My Free Consultation
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="contact-info text-center mt-4">
                    <p class="text-muted">
                        <small>
                            <i class="fas fa-clock me-1"></i>
                            We'll contact you within 24 hours to confirm your consultation time.
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.schedule-container {
    padding-top: 120px; /* Account for fixed navbar */
    padding-bottom: 3rem;
    min-height: 80vh;
}

.success-icon {
    font-size: 4rem;
    color: var(--success);
}

.schedule-header h1 {
    color: var(--dark);
    font-weight: 700;
    margin-bottom: 1rem;
}

.score-display {
    margin: 2rem 0;
}

.score-circle {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    font-weight: bold;
    box-shadow: var(--box-shadow);
}

.score-number {
    font-size: 2rem;
    line-height: 1;
}

.score-label {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
}

.card-title {
    color: var(--dark);
    font-weight: 600;
}

.form-label {
    color: var(--dark);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid var(--gray-light);
    border-radius: 10px;
    padding: 1rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(255, 140, 56, 0.25);
}

.consultation-benefits {
    background: var(--primary-soft);
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid var(--primary);
}

.consultation-benefits h5 {
    color: var(--dark);
    font-weight: 600;
    margin-bottom: 1rem;
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefits-list li {
    padding: 0.5rem 0;
    color: var(--gray);
    font-weight: 500;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    padding: 1rem 2.5rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-hover);
}

.contact-info {
    background: var(--secondary);
    padding: 1rem;
    border-radius: 10px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .schedule-container {
        padding-top: 100px; /* Slightly less padding on tablets */
    }
}

@media (max-width: 768px) {
    .schedule-container {
        padding-top: 90px; /* Less padding on mobile */
        padding-bottom: 2rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .success-icon {
        font-size: 3rem;
    }

    .score-circle {
        width: 100px;
        height: 100px;
    }

    .score-number {
        font-size: 1.5rem;
    }

    .consultation-benefits {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .schedule-container {
        padding-top: 80px; /* Even less padding on small mobile */
    }

    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}
</style>
@endsection
