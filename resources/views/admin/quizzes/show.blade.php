@extends('components.layouts.admin')

@section('title', 'Quiz Management - ' . $landingPage->title)

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Quiz Management</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.landing-pages.index') }}">Landing Pages</a></li>
                        <li class="breadcrumb-item active">Quiz Management</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Action Buttons -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{{ route('admin.quiz-categories.index', $landingPage) }}" class="btn btn-outline-success mr-2">
                                <i class="fas fa-folder mr-2"></i>Categories
                            </a>
                            <a href="{{ route('admin.landing-pages.show', $landingPage) }}" class="btn btn-outline-primary mr-2">
                                <i class="fas fa-file-alt mr-2"></i>Landing Page
                            </a>
                            <a href="{{ route('admin.landing-pages.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left mr-2"></i>Back
                            </a>
                        </div>
                    </div>
                </div>
            </div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
@endif

<div class="row">
    <div class="col-lg-8">
        <!-- Quiz Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Quiz Settings</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.quizzes.update', $landingPage) }}">
                    @csrf
                    @method('PUT')

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">Quiz Title</label>
                                <input type="text" class="form-control" id="title" name="title" value="{{ old('title', $quiz->title) }}" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', $quiz->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ old('description', $quiz->description) }}</textarea>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-2"></i>Update Quiz
                    </button>
                </form>
            </div>
        </div>

        <!-- Quiz Questions -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Questions ({{ $quiz->questions->count() }})</h5>
                <button type="button" class="btn btn-highlight" data-toggle="modal" data-target="#addQuestionModal">
                    <i class="fas fa-plus mr-2"></i>Add Question
                </button>
            </div>
            <div class="card-body">
                @if($quiz->questions->count() > 0)
                    <div id="questions-list">
                        @foreach($quiz->questions as $question)
                            <div class="question-item border rounded p-3 mb-3" data-question-id="{{ $question->id }}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="badge bg-secondary mr-2">{{ $question->sort_order }}</span>
                                            @if($question->category)
                                                <span class="badge mr-2" style="background-color: {{ $question->category->color }}; color: white;">
                                                    {{ $question->category->name }}
                                                </span>
                                            @endif
                                            <span class="badge bg-info mr-2">{{ ucfirst(str_replace('_', ' ', $question->type)) }}</span>
                                            <span class="badge bg-primary">{{ $question->points }} pts</span>
                                            @if($question->is_required)
                                                <span class="badge bg-warning ml-2">Required</span>
                                            @endif
                                        </div>
                                        <h6 class="mb-2">{{ $question->question }}</h6>

                                        @if($question->hasOptions())
                                            <div class="options-preview">
                                                <small class="text-muted">Options:</small>
                                                <ul class="list-unstyled ml-3 mb-0">
                                                    @foreach($question->getFormattedOptions() as $key => $option)
                                                        <li class="small">
                                                            <i class="fas fa-circle fa-xs mr-1"></i>
                                                            {{ $option['text'] }} ({{ $option['points'] }} pts)
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="btn-group ml-3">
                                        <button type="button" class="btn btn-sm btn-outline-secondary"
                                                data-toggle="modal"
                                                data-target="#editQuestionModal{{ $question->id }}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                data-toggle="modal"
                                                data-target="#deleteQuestionModal{{ $question->id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary drag-handle">
                                            <i class="fas fa-grip-vertical"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Edit Question Modal -->
                            <div class="modal fade" id="editQuestionModal{{ $question->id }}" tabindex="-1">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Edit Question</h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                                        </div>
                                        <form method="POST" action="{{ route('admin.quizzes.questions.update', [$landingPage, $question]) }}">
                                            @csrf
                                            @method('PUT')
                                            <div class="modal-body">
                                                <!-- Question form fields would go here -->
                                                <div class="mb-3">
                                                    <label class="form-label">Question</label>
                                                    <input type="text" class="form-control" name="question" value="{{ $question->question }}" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Category</label>
                                                    <select class="form-select" name="category_id">
                                                        <option value="">No Category</option>
                                                        @foreach($categories as $category)
                                                            <option value="{{ $category->id }}" {{ $question->category_id == $category->id ? 'selected' : '' }}>
                                                                {{ $category->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label class="form-label">Type</label>
                                                            <select class="form-select" name="type" required>
                                                                @foreach(\App\Models\QuizQuestion::getTypes() as $value => $label)
                                                                    <option value="{{ $value }}" {{ $question->type === $value ? 'selected' : '' }}>
                                                                        {{ $label }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label class="form-label">Points</label>
                                                            <input type="number" class="form-control" name="points" value="{{ $question->points }}" min="1" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div id="edit-options-container-{{ $question->id }}" style="{{ $question->hasOptions() ? '' : 'display: none;' }}">
                                                    <label class="form-label">Options</label>
                                                    <div id="edit-options-list-{{ $question->id }}">
                                                        @if($question->hasOptions())
                                                            @foreach($question->getFormattedOptions() as $key => $option)
                                                                <div class="input-group mb-2">
                                                                    <input type="text" class="form-control" name="options[{{ $key }}][text]" value="{{ $option['text'] }}" placeholder="Option text" required>
                                                                    <input type="number" class="form-control" name="options[{{ $key }}][points]" value="{{ $option['points'] }}" placeholder="Points" min="0" style="max-width: 100px;" required>
                                                                    <button type="button" class="btn btn-outline-danger remove-option">
                                                                        <i class="fas fa-times"></i>
                                                                    </button>
                                                                </div>
                                                            @endforeach
                                                        @endif
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary add-option" data-question-id="{{ $question->id }}">
                                                        <i class="fas fa-plus mr-1"></i>Add Option
                                                    </button>
                                                </div>

                                                <div class="form-check mt-3">
                                                    <input class="form-check-input" type="checkbox" name="is_required" value="1" {{ $question->is_required ? 'checked' : '' }}>
                                                    <label class="form-check-label">Required</label>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                <button type="submit" class="btn btn-primary">Update Question</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Delete Question Modal -->
                            <div class="modal fade" id="deleteQuestionModal{{ $question->id }}" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Delete Question</h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                                <span>&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Are you sure you want to delete this question?</p>
                                            <p class="text-muted small">{{ $question->question }}</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                            <form method="POST" action="{{ route('admin.quizzes.questions.destroy', [$landingPage, $question]) }}" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger">Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No questions yet</h6>
                        <p class="text-muted">Add questions to create your assessment quiz.</p>
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addQuestionModal">
                            <i class="fas fa-plus mr-2"></i>Add First Question
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Quiz Statistics -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Quiz Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-primary mb-1">{{ $quiz->questions->count() }}</h4>
                            <small class="text-muted">Questions</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-info mb-1">{{ $quiz->getTotalPossibleScore() }}</h4>
                            <small class="text-muted">Max Score</small>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="border rounded p-3 {{ $quiz->is_active ? 'bg-success text-white' : 'bg-secondary text-white' }}">
                            <i class="fas fa-{{ $quiz->is_active ? 'check-circle' : 'pause-circle' }} fa-2x mb-2"></i>
                            <div><small>{{ $quiz->is_active ? 'Active' : 'Inactive' }}</small></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" data-toggle="modal" data-target="#addQuestionModal">
                        <i class="fas fa-plus mr-2"></i>Add Question
                    </button>
                    <a href="{{ route('admin.quiz-categories.index', $landingPage) }}" class="btn btn-outline-success">
                        <i class="fas fa-folder mr-2"></i>Manage Categories
                    </a>
                    <a href="{{ route('admin.quizzes.score-config', $landingPage) }}" class="btn btn-outline-warning">
                        <i class="fas fa-gauge-high mr-2"></i>Score Configuration
                    </a>
                    <a href="{{ route('landing-pages.quiz', $landingPage) }}" target="_blank" class="btn btn-outline-secondary">
                        <i class="fas fa-external-link-alt mr-2"></i>Preview Quiz
                    </a>
                    <a href="{{ route('admin.leads.index', ['landing_page_id' => $landingPage->id]) }}" class="btn btn-outline-info">
                        <i class="fas fa-users mr-2"></i>View Responses
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Question Modal -->
<div class="modal fade" id="addQuestionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Question</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('admin.quizzes.questions.store', $landingPage) }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="question" class="form-label">Question *</label>
                        <input type="text" class="form-control" id="question" name="question" required>
                    </div>

                    <div class="mb-3">
                        <label for="category_id" class="form-label">Category</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">No Category</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                            @endforeach
                        </select>
                        <div class="form-text">
                            <a href="{{ route('admin.quiz-categories.index', $landingPage) }}" target="_blank">
                                <i class="fas fa-plus mr-1"></i>Manage Categories
                            </a>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">Question Type *</label>
                                <select class="form-select" id="type" name="type" required>
                                    @foreach(\App\Models\QuizQuestion::getTypes() as $value => $label)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="points" class="form-label">Points *</label>
                                <input type="number" class="form-control" id="points" name="points" value="10" min="1" required>
                            </div>
                        </div>
                    </div>

                    <div id="options-container" style="display: none;">
                        <label class="form-label">Options</label>
                        <div id="options-list">
                            <!-- Options will be added dynamically -->
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="add-option">
                            <i class="fas fa-plus mr-1"></i>Add Option
                        </button>
                    </div>

                    <div class="form-check mt-3">
                        <input class="form-check-input" type="checkbox" id="is_required" name="is_required" value="1" checked>
                        <label class="form-check-label" for="is_required">Required</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Question</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Quiz management script loaded');

    // Add Question Modal
    const typeSelect = document.getElementById('type');
    const optionsContainer = document.getElementById('options-container');
    const optionsList = document.getElementById('options-list');
    const addOptionBtn = document.getElementById('add-option');

    console.log('Elements found:', {
        typeSelect: !!typeSelect,
        optionsContainer: !!optionsContainer,
        optionsList: !!optionsList,
        addOptionBtn: !!addOptionBtn
    });

    let optionIndex = 0;

    function toggleOptionsContainer() {
        const selectedType = typeSelect.value;
        if (selectedType === 'multiple_choice' || selectedType === 'single_choice') {
            optionsContainer.style.display = 'block';
            if (optionsList.children.length === 0) {
                addOption();
                addOption();
            }
        } else {
            optionsContainer.style.display = 'none';
            optionsList.innerHTML = '';
            optionIndex = 0;
        }
    }

    function addOption() {
        const optionDiv = document.createElement('div');
        optionDiv.className = 'input-group mb-2';
        optionDiv.innerHTML = `
            <input type="text" class="form-control" name="options[${optionIndex}][text]" placeholder="Option text" required>
            <input type="number" class="form-control" name="options[${optionIndex}][points]" placeholder="Points" value="1" min="0" style="max-width: 100px;" required>
            <button type="button" class="btn btn-outline-danger remove-option">
                <i class="fas fa-times"></i>
            </button>
        `;

        optionsList.appendChild(optionDiv);
        optionIndex++;

        // Add remove functionality
        optionDiv.querySelector('.remove-option').addEventListener('click', function() {
            optionDiv.remove();
        });
    }

    typeSelect.addEventListener('change', toggleOptionsContainer);
    addOptionBtn.addEventListener('click', addOption);

    // Initialize
    toggleOptionsContainer();

    // Edit Question Modals
    document.querySelectorAll('[id^="editQuestionModal"]').forEach(function(modal) {
        const questionId = modal.id.replace('editQuestionModal', '');
        const editTypeSelect = modal.querySelector('select[name="type"]');
        const editOptionsContainer = document.getElementById('edit-options-container-' + questionId);
        const editOptionsList = document.getElementById('edit-options-list-' + questionId);
        const editAddOptionBtn = modal.querySelector('.add-option');

        let editOptionIndex = editOptionsList ? editOptionsList.children.length : 0;

        function toggleEditOptionsContainer() {
            const selectedType = editTypeSelect.value;
            if (selectedType === 'multiple_choice' || selectedType === 'single_choice') {
                editOptionsContainer.style.display = 'block';
                if (editOptionsList.children.length === 0) {
                    addEditOption();
                    addEditOption();
                }
            } else {
                editOptionsContainer.style.display = 'none';
                editOptionsList.innerHTML = '';
                editOptionIndex = 0;
            }
        }

        function addEditOption() {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'input-group mb-2';
            optionDiv.innerHTML = `
                <input type="text" class="form-control" name="options[${editOptionIndex}][text]" placeholder="Option text" required>
                <input type="number" class="form-control" name="options[${editOptionIndex}][points]" placeholder="Points" value="1" min="0" style="max-width: 100px;" required>
                <button type="button" class="btn btn-outline-danger remove-option">
                    <i class="fas fa-times"></i>
                </button>
            `;

            editOptionsList.appendChild(optionDiv);
            editOptionIndex++;

            // Add remove functionality
            optionDiv.querySelector('.remove-option').addEventListener('click', function() {
                optionDiv.remove();
            });
        }

        editTypeSelect.addEventListener('change', toggleEditOptionsContainer);
        editAddOptionBtn.addEventListener('click', addEditOption);

        // Handle existing remove buttons
        editOptionsList.querySelectorAll('.remove-option').forEach(function(btn) {
            btn.addEventListener('click', function() {
                btn.closest('.input-group').remove();
            });
        });
    });

    // Initialize sortable for questions
    if (typeof Sortable !== 'undefined') {
        const questionsList = document.getElementById('questions-list');
        if (questionsList) {
            new Sortable(questionsList, {
                handle: '.drag-handle',
                animation: 150,
                onEnd: function(evt) {
                    const questionIds = Array.from(questionsList.children).map(item =>
                        item.getAttribute('data-question-id')
                    );

                    fetch('{{ route("admin.quizzes.questions.reorder", $landingPage) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            questions: questionIds
                        })
                    });
                }
            });
        }
    }
});
</script>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
