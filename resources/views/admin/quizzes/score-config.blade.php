@extends('components.layouts.admin')

@section('title', 'Score Configuration - ' . $landingPage->title)

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Score Configuration</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.landing-pages.index') }}">Landing Pages</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.landing-pages.show', $landingPage) }}">{{ $landingPage->title }}</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.quizzes.show', $landingPage) }}">Quiz</a></li>
                        <li class="breadcrumb-item active">Score Configuration</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-gauge-high mr-2"></i>
                                Configure Score Display & Results
                            </h3>
                            <div class="card-tools">
                                <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-arrow-left mr-1"></i>
                                    Back to Quiz
                                </a>
                            </div>
                        </div>

                        <form action="{{ route('admin.quizzes.score-config.update', $landingPage) }}" method="POST">
                            @csrf
                            @method('PUT')

                            <div class="card-body">
                                <!-- Gauge Configuration -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <h4><i class="fas fa-gauge mr-2"></i>Gauge Settings</h4>

                                        <div class="form-group">
                                            <label for="gauge_config_min_value">Minimum Value</label>
                                            <input type="number" class="form-control" id="gauge_config_min_value"
                                                   name="gauge_config[min_value]" value="{{ $gaugeConfig['min_value'] }}" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="gauge_config_max_value">Maximum Value</label>
                                            <input type="number" class="form-control" id="gauge_config_max_value"
                                                   name="gauge_config[max_value]" value="{{ $gaugeConfig['max_value'] }}" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="gauge_config_gauge_type">Gauge Type</label>
                                            <select class="form-control" id="gauge_config_gauge_type" name="gauge_config[gauge_type]" required>
                                                <option value="semicircle" {{ $gaugeConfig['gauge_type'] === 'semicircle' ? 'selected' : '' }}>Semi-circle</option>
                                                <option value="full_circle" {{ $gaugeConfig['gauge_type'] === 'full_circle' ? 'selected' : '' }}>Full Circle</option>
                                                <option value="linear" {{ $gaugeConfig['gauge_type'] === 'linear' ? 'selected' : '' }}>Linear</option>
                                            </select>
                                        </div>

                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="gauge_config_show_percentage"
                                                   name="gauge_config[show_percentage]" {{ $gaugeConfig['show_percentage'] ? 'checked' : '' }}>
                                            <label class="form-check-label" for="gauge_config_show_percentage">
                                                Show Percentage Symbol
                                            </label>
                                        </div>

                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="gauge_config_show_labels"
                                                   name="gauge_config[show_labels]" {{ $gaugeConfig['show_labels'] ? 'checked' : '' }}>
                                            <label class="form-check-label" for="gauge_config_show_labels">
                                                Show Range Labels
                                            </label>
                                        </div>

                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="gauge_config_animate"
                                                   name="gauge_config[animate]" {{ $gaugeConfig['animate'] ? 'checked' : '' }}>
                                            <label class="form-check-label" for="gauge_config_animate">
                                                Enable Animation
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h4><i class="fas fa-palette mr-2"></i>Color Ranges</h4>
                                        <div id="color-ranges">
                                            @foreach($gaugeConfig['color_ranges'] as $index => $range)
                                                <div class="color-range-item border p-3 mb-3 rounded">
                                                    <div class="row">
                                                        <div class="col-md-3">
                                                            <label>Min</label>
                                                            <input type="number" class="form-control"
                                                                   name="gauge_config[color_ranges][{{ $index }}][min]"
                                                                   value="{{ $range['min'] }}" required>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <label>Max</label>
                                                            <input type="number" class="form-control"
                                                                   name="gauge_config[color_ranges][{{ $index }}][max]"
                                                                   value="{{ $range['max'] }}" required>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <label>Color</label>
                                                            <input type="color" class="form-control"
                                                                   name="gauge_config[color_ranges][{{ $index }}][color]"
                                                                   value="{{ $range['color'] }}" required>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <label>Label</label>
                                                            <input type="text" class="form-control"
                                                                   name="gauge_config[color_ranges][{{ $index }}][label]"
                                                                   value="{{ $range['label'] }}" required>
                                                        </div>
                                                    </div>
                                                    <button type="button" class="btn btn-danger btn-sm mt-2 remove-color-range">
                                                        <i class="fas fa-trash"></i> Remove
                                                    </button>
                                                </div>
                                            @endforeach
                                        </div>
                                        <button type="button" class="btn btn-success btn-sm" id="add-color-range">
                                            <i class="fas fa-plus"></i> Add Color Range
                                        </button>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <!-- Result Text Configuration -->
                                <h4><i class="fas fa-comment-alt mr-2"></i>Result Text Configuration</h4>
                                <div id="result-texts">
                                    @foreach($resultTexts['ranges'] as $index => $range)
                                        <div class="result-text-item border p-3 mb-3 rounded">
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <label>Min Score</label>
                                                    <input type="number" class="form-control"
                                                           name="result_texts[ranges][{{ $index }}][min]"
                                                           value="{{ $range['min'] }}" required>
                                                </div>
                                                <div class="col-md-2">
                                                    <label>Max Score</label>
                                                    <input type="number" class="form-control"
                                                           name="result_texts[ranges][{{ $index }}][max]"
                                                           value="{{ $range['max'] }}" required>
                                                </div>
                                                <div class="col-md-8">
                                                    <label>Title</label>
                                                    <input type="text" class="form-control"
                                                           name="result_texts[ranges][{{ $index }}][title]"
                                                           value="{{ $range['title'] }}" required>
                                                </div>
                                            </div>
                                            <div class="form-group mt-3">
                                                <label>Description</label>
                                                <textarea class="form-control" rows="3"
                                                          name="result_texts[ranges][{{ $index }}][description]" required>{{ $range['description'] }}</textarea>
                                            </div>
                                            <div class="form-group">
                                                <label>Recommendations (one per line)</label>
                                                <textarea class="form-control" rows="4"
                                                          name="result_texts[ranges][{{ $index }}][recommendations_text]"
                                                          placeholder="Enter recommendations, one per line">{{ isset($range['recommendations']) ? implode("\n", $range['recommendations']) : '' }}</textarea>
                                            </div>
                                            <button type="button" class="btn btn-danger btn-sm remove-result-text">
                                                <i class="fas fa-trash"></i> Remove
                                            </button>
                                        </div>
                                    @endforeach
                                </div>
                                <button type="button" class="btn btn-success btn-sm" id="add-result-text">
                                    <i class="fas fa-plus"></i> Add Result Text Range
                                </button>
                            </div>

                            <div class="card-footer">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save mr-1"></i>
                                    Save Configuration
                                </button>
                                <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-secondary">
                                    Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let colorRangeIndex = {{ count($gaugeConfig['color_ranges']) }};
    let resultTextIndex = {{ count($resultTexts['ranges']) }};

    // Add color range functionality
    document.getElementById('add-color-range').addEventListener('click', function() {
        const container = document.getElementById('color-ranges');
        const newRange = document.createElement('div');
        newRange.className = 'color-range-item border p-3 mb-3 rounded';
        newRange.innerHTML = `
            <div class="row">
                <div class="col-md-3">
                    <label>Min</label>
                    <input type="number" class="form-control"
                           name="gauge_config[color_ranges][${colorRangeIndex}][min]"
                           value="0" required>
                </div>
                <div class="col-md-3">
                    <label>Max</label>
                    <input type="number" class="form-control"
                           name="gauge_config[color_ranges][${colorRangeIndex}][max]"
                           value="100" required>
                </div>
                <div class="col-md-3">
                    <label>Color</label>
                    <input type="color" class="form-control"
                           name="gauge_config[color_ranges][${colorRangeIndex}][color]"
                           value="#48bb78" required>
                </div>
                <div class="col-md-3">
                    <label>Label</label>
                    <input type="text" class="form-control"
                           name="gauge_config[color_ranges][${colorRangeIndex}][label]"
                           value="Range ${colorRangeIndex + 1}" required>
                </div>
            </div>
            <button type="button" class="btn btn-danger btn-sm mt-2 remove-color-range">
                <i class="fas fa-trash"></i> Remove
            </button>
        `;
        container.appendChild(newRange);
        colorRangeIndex++;
    });

    // Remove color range functionality
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-color-range') || e.target.closest('.remove-color-range')) {
            const item = e.target.closest('.color-range-item');
            if (document.querySelectorAll('.color-range-item').length > 1) {
                item.remove();
            } else {
                alert('At least one color range is required.');
            }
        }
    });

    // Add result text functionality
    document.getElementById('add-result-text').addEventListener('click', function() {
        const container = document.getElementById('result-texts');
        const newResultText = document.createElement('div');
        newResultText.className = 'result-text-item border p-3 mb-3 rounded';
        newResultText.innerHTML = `
            <div class="row">
                <div class="col-md-2">
                    <label>Min Score</label>
                    <input type="number" class="form-control"
                           name="result_texts[ranges][${resultTextIndex}][min]"
                           value="0" required>
                </div>
                <div class="col-md-2">
                    <label>Max Score</label>
                    <input type="number" class="form-control"
                           name="result_texts[ranges][${resultTextIndex}][max]"
                           value="100" required>
                </div>
                <div class="col-md-8">
                    <label>Title</label>
                    <input type="text" class="form-control"
                           name="result_texts[ranges][${resultTextIndex}][title]"
                           value="New Range" required>
                </div>
            </div>
            <div class="form-group mt-3">
                <label>Description</label>
                <textarea class="form-control" rows="3"
                          name="result_texts[ranges][${resultTextIndex}][description]" required></textarea>
            </div>
            <div class="form-group">
                <label>Recommendations (one per line)</label>
                <textarea class="form-control" rows="4"
                          name="result_texts[ranges][${resultTextIndex}][recommendations_text]"
                          placeholder="Enter recommendations, one per line"></textarea>
            </div>
            <button type="button" class="btn btn-danger btn-sm remove-result-text">
                <i class="fas fa-trash"></i> Remove
            </button>
        `;
        container.appendChild(newResultText);
        resultTextIndex++;
    });

    // Remove result text functionality
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-result-text') || e.target.closest('.remove-result-text')) {
            const item = e.target.closest('.result-text-item');
            if (document.querySelectorAll('.result-text-item').length > 1) {
                item.remove();
            } else {
                alert('At least one result text range is required.');
            }
        }
    });

    // Form submission processing for recommendations
    document.querySelector('form').addEventListener('submit', function(e) {
        // Convert recommendations text to arrays
        const recommendationTextareas = document.querySelectorAll('textarea[name*="recommendations_text"]');
        recommendationTextareas.forEach(function(textarea, index) {
            const lines = textarea.value.split('\n').filter(line => line.trim() !== '');
            lines.forEach(function(line, lineIndex) {
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = `result_texts[ranges][${index}][recommendations][${lineIndex}]`;
                hiddenInput.value = line.trim();
                textarea.parentNode.appendChild(hiddenInput);
            });
        });
    });
});
</script>
@endsection
